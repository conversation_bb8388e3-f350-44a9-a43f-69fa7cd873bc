/**
 * Stock utility functions for consistent symbol detection and API routing
 */

// Stock symbol detection utilities
export const isIndianStock = (symbol: string): boolean => {
  return symbol.endsWith('.NS') || symbol.endsWith('.BO');
};

export const isUSStock = (symbol: string): boolean => {
  return !isIndianStock(symbol) && !isIndianIndex(symbol);
};

export const isIndianIndex = (symbol: string): boolean => {
  const indianIndices = ['^NSEI', '^BSESN', '^NSEBANK', '^NSEIT'];
  return indianIndices.includes(symbol);
};

export const isUSIndex = (symbol: string): boolean => {
  const usIndices = ['^GSPC', '^IXIC', '^DJI', '^VIX'];
  return usIndices.includes(symbol);
};

// Symbol categorization
export const categorizeSymbols = (symbols: string[]) => {
  const indianStocks: string[] = [];
  const usStocks: string[] = [];
  const indianIndices: string[] = [];
  const usIndices: string[] = [];

  symbols.forEach(symbol => {
    if (isIndianStock(symbol)) {
      indianStocks.push(symbol);
    } else if (isIndianIndex(symbol)) {
      indianIndices.push(symbol);
    } else if (isUSIndex(symbol)) {
      usIndices.push(symbol);
    } else {
      usStocks.push(symbol);
    }
  });

  return {
    indianStocks,
    usStocks,
    indianIndices,
    usIndices,
  };
};

// Data source determination
export const getDataSource = (symbol: string): 'yahoo' | 'finnhub' => {
  return isIndianStock(symbol) || isIndianIndex(symbol) ? 'yahoo' : 'finnhub';
};

// Safe number formatting to prevent toFixed errors
export const safeToFixed = (value: number | undefined | null, decimals: number = 2): string => {
  const safeValue = value || 0;
  return safeValue.toFixed(decimals);
};

// Safe percentage formatting
export const safePercentage = (value: number | undefined | null, decimals: number = 2): string => {
  return `${safeToFixed(value, decimals)}%`;
};

// Validate and sanitize stock data to prevent runtime errors
export const sanitizeStockData = (data: any): any => {
  if (!data) return null;

  return {
    ...data,
    price: data.price || 0,
    change: data.change || 0,
    changePercent: data.changePercent || 0,
    high: data.high || 0,
    low: data.low || 0,
    open: data.open || 0,
    previousClose: data.previousClose || 0,
    volume: data.volume || 0,
  };
};

// Currency formatting based on symbol
export const formatPrice = (price: number, symbol: string): string => {
  const safePrice = price || 0;
  if (isIndianStock(symbol) || isIndianIndex(symbol)) {
    return `₹${safePrice.toLocaleString('en-IN', { maximumFractionDigits: 2 })}`;
  }
  return `$${safePrice.toFixed(2)}`;
};

// Exchange detection
export const getExchange = (symbol: string): string => {
  if (symbol.endsWith('.NS')) return 'NSE';
  if (symbol.endsWith('.BO')) return 'BSE';
  if (symbol.startsWith('^NSEI') || symbol.startsWith('^NSEBANK') || symbol.startsWith('^NSEIT')) return 'NSE';
  if (symbol.startsWith('^BSESN')) return 'BSE';
  return 'US';
};

// Clean symbol display (remove exchange suffixes)
export const getDisplaySymbol = (symbol: string): string => {
  return symbol.replace('.NS', '').replace('.BO', '');
};

// Market hours detection
export const getMarketRegion = (symbol: string): 'IN' | 'US' => {
  return isIndianStock(symbol) || isIndianIndex(symbol) ? 'IN' : 'US';
};

// Data structure normalization helpers
export interface NormalizedQuote {
  symbol: string;
  name?: string;
  price: number;
  change: number;
  changePercent: number;
  high: number;
  low: number;
  open: number;
  previousClose: number;
  volume?: number;
  currency: string;
  exchange: string;
  timestamp: number;
  marketState?: string;
}

// Normalize Yahoo Finance quote to common format
export const normalizeYahooQuote = (quote: any): NormalizedQuote => {
  return {
    symbol: quote.symbol,
    name: quote.name,
    price: quote.price,
    change: quote.change,
    changePercent: quote.changePercent,
    high: quote.high,
    low: quote.low,
    open: quote.open,
    previousClose: quote.previousClose,
    volume: quote.volume,
    currency: quote.currency || 'INR',
    exchange: quote.exchange,
    timestamp: quote.timestamp,
    marketState: quote.marketState,
  };
};

// Normalize Finnhub quote to common format
export const normalizeFinnhubQuote = (quote: any, symbol: string): NormalizedQuote => {
  return {
    symbol,
    price: quote.c,
    change: quote.d,
    changePercent: quote.dp,
    high: quote.h,
    low: quote.l,
    open: quote.o,
    previousClose: quote.pc,
    currency: 'USD',
    exchange: 'US',
    timestamp: quote.t,
  };
};

// Batch symbol processing
export const processMixedSymbols = (symbols: string[]) => {
  const categorized = categorizeSymbols(symbols);
  
  return {
    needsYahooFinance: [...categorized.indianStocks, ...categorized.indianIndices],
    needsFinnhub: [...categorized.usStocks, ...categorized.usIndices],
    categorized,
  };
};

// Error handling helpers
export const getAPIErrorMessage = (error: any, symbol: string): string => {
  const dataSource = getDataSource(symbol);
  const apiName = dataSource === 'yahoo' ? 'Yahoo Finance' : 'Finnhub';
  
  if (error.message?.includes('timeout')) {
    return `${apiName} API request timed out for ${symbol}. Please try again.`;
  }
  
  if (error.response?.status === 429) {
    return `${apiName} API rate limit exceeded. Please wait a moment and try again.`;
  }
  
  return `Failed to fetch data for ${symbol} from ${apiName} API.`;
};
