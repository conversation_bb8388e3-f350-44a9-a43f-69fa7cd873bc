
import React, { useMemo } from 'react';
import { <PERSON>U<PERSON>, <PERSON>Down, Loader2, AlertCircle } from 'lucide-react';
import { useBatchStockQuotes } from '@/hooks/useStockData';
import { useBatchIndianStockQuotes } from '@/hooks/useIndianStockData';
import { POPULAR_US_STOCKS } from '@/integrations/finnhub/types';
import { formatPrice } from '@/utils/stockUtils';

// Ticker speed configuration
export type TickerSpeed = 'slow' | 'normal' | 'fast';

interface MarketTickerProps {
  speed?: TickerSpeed;
  pauseOnHover?: boolean;
  showCompanyNames?: boolean;
}

// Separate symbols by data source
const INDIAN_TICKER_SYMBOLS = [
  'RELIANCE.NS',
  'TCS.NS',
  'HDFCBANK.NS',
  'INFY.NS',
  'HINDUNILVR.NS',
  'ICICIBANK.NS',
  'BHARTIARTL.NS',
  'ITC.NS',
];

const US_TICKER_SYMBOLS = [
  'AAPL',
  'GOOGL',
  'MSFT',
  'AMZN',
  'TSLA',
  'META',
  'NVDA',
  'SPY', // S&P 500 ETF
  'QQQ', // NASDAQ ETF
];

// Stock name mappings for display
const STOCK_NAMES: Record<string, string> = {
  'RELIANCE.NS': 'RELIANCE',
  'TCS.NS': 'TCS',
  'HDFCBANK.NS': 'HDFC BANK',
  'INFY.NS': 'INFOSYS',
  'HINDUNILVR.NS': 'HINDUSTAN UNILEVER',
  'ICICIBANK.NS': 'ICICI BANK',
  'BHARTIARTL.NS': 'BHARTI AIRTEL',
  'ITC.NS': 'ITC',
  'AAPL': 'APPLE',
  'GOOGL': 'GOOGLE',
  'MSFT': 'MICROSOFT',
  'AMZN': 'AMAZON',
  'TSLA': 'TESLA',
  'META': 'META',
  'NVDA': 'NVIDIA',
  'SPY': 'S&P 500',
  'QQQ': 'NASDAQ',
};

// formatPrice function moved to utils/stockUtils.ts

const MarketTicker: React.FC<MarketTickerProps> = ({
  speed = 'normal',
  pauseOnHover = true,
  showCompanyNames = true
}) => {
  // Fetch Indian stocks from Yahoo Finance
  const { data: indianStocksData, isLoading: indianLoading, error: indianError } = useBatchIndianStockQuotes(INDIAN_TICKER_SYMBOLS);

  // Fetch US stocks from Finnhub
  const { data: usStocksData, isLoading: usLoading, error: usError } = useBatchStockQuotes(US_TICKER_SYMBOLS);

  const isLoading = indianLoading || usLoading;
  const error = indianError || usError;

  // Combine the data from both sources
  const batchQuotes = React.useMemo(() => {
    const combined: any[] = [];

    // Add Indian stock data (normalized)
    if (indianStocksData) {
      indianStocksData.forEach(item => {
        if (item.data) {
          combined.push({
            symbol: item.symbol,
            data: {
              c: item.data.price,
              d: item.data.change,
              dp: item.data.changePercent,
            },
            error: item.error,
          });
        }
      });
    }

    // Add US stock data (handle array properly)
    if (usStocksData && Array.isArray(usStocksData)) {
      combined.push(...usStocksData);
    }

    return combined;
  }, [indianStocksData, usStocksData]);

  // Calculate animation class based on speed and data amount
  const getAnimationClass = useMemo(() => {
    const dataCount = batchQuotes?.length || 0;

    // Base speed selection
    let baseSpeed = speed;

    // Auto-adjust speed based on data amount for better readability
    if (dataCount > 15) {
      baseSpeed = 'slow'; // Lots of data = slower speed
    } else if (dataCount < 8) {
      baseSpeed = 'fast'; // Less data = can be faster
    }

    const speedMap = {
      slow: 'animate-marquee-slow',
      normal: 'animate-marquee',
      fast: 'animate-marquee-fast'
    };

    return speedMap[baseSpeed];
  }, [speed, batchQuotes?.length]);

  // Loading state
  if (isLoading) {
    return (
      <div className="stock-marquee">
        <div className="marquee-content">
          <div className="market-item">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            <span>Loading market data...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="stock-marquee">
        <div className="marquee-content">
          <div className="market-item">
            <AlertCircle className="h-4 w-4 mr-2 text-red-400" />
            <span>Market data unavailable</span>
          </div>
        </div>
      </div>
    );
  }

  // Filter successful quotes and prepare display data
  const validQuotes = batchQuotes?.filter(quote => quote.data && !quote.error) || [];

  if (validQuotes.length === 0) {
    return (
      <div className="stock-marquee">
        <div className="marquee-content">
          <div className="market-item">
            <span>No market data available</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="stock-marquee">
      <div
        className={`marquee-content ${getAnimationClass} ${pauseOnHover ? 'hover:pause' : ''}`}
        style={{
          animationPlayState: pauseOnHover ? 'running' : undefined,
        }}
      >
        {validQuotes.map((quote, index) => {
          const { symbol, data } = quote;
          if (!data) return null;

          const displayName = STOCK_NAMES[symbol] || symbol;
          const price = formatPrice(data.c, symbol);
          const change = data.d;
          const changePercent = data.dp;

          return (
            <div key={`${symbol}-${index}`} className="market-item">
              {showCompanyNames && (
                <span className="font-medium text-white mr-2">{displayName}</span>
              )}
              <span className="text-white font-semibold">{price}</span>
              <span
                className={`flex items-center ${
                  change > 0
                    ? "stock-up"
                    : change < 0
                    ? "stock-down"
                    : "stock-neutral"
                }`}
              >
                {change > 0 ? (
                  <ArrowUp className="h-3 w-3 mr-1" />
                ) : change < 0 ? (
                  <ArrowDown className="h-3 w-3 mr-1" />
                ) : null}
                {change !== 0 && (
                  <>
                    {Math.abs(change || 0).toFixed(2)} ({Math.abs(changePercent || 0).toFixed(2)}%)
                  </>
                )}
                {change === 0 && "0.00%"}
              </span>
            </div>
          );
        })}
        {/* Duplicate content for seamless scrolling */}
        {validQuotes.map((quote, index) => {
          const { symbol, data } = quote;
          if (!data) return null;

          const displayName = STOCK_NAMES[symbol] || symbol;
          const price = formatPrice(data.c, symbol);
          const change = data.d;
          const changePercent = data.dp;

          return (
            <div key={`${symbol}-duplicate-${index}`} className="market-item">
              {showCompanyNames && (
                <span className="font-medium text-white mr-2">{displayName}</span>
              )}
              <span className="text-white font-semibold">{price}</span>
              <span
                className={`flex items-center ${
                  change > 0
                    ? "stock-up"
                    : change < 0
                    ? "stock-down"
                    : "stock-neutral"
                }`}
              >
                {change > 0 ? (
                  <ArrowUp className="h-3 w-3 mr-1" />
                ) : change < 0 ? (
                  <ArrowDown className="h-3 w-3 mr-1" />
                ) : null}
                {change !== 0 && (
                  <>
                    {Math.abs(change || 0).toFixed(2)} ({Math.abs(changePercent || 0).toFixed(2)}%)
                  </>
                )}
                {change === 0 && "0.00%"}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MarketTicker;
