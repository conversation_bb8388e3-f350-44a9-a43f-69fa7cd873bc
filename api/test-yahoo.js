/**
 * Test endpoint for Yahoo Finance proxy
 */

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    // Test with a simple Yahoo Finance request
    const testSymbol = 'RELIANCE.NS';
    const url = `https://query1.finance.yahoo.com/v8/finance/chart/${testSymbol}?interval=1d&period=1d&region=IN`;
    
    console.log(`Testing Yahoo Finance API with: ${url}`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Yahoo Finance API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    res.status(200).json({
      success: true,
      message: 'Yahoo Finance API is working',
      testSymbol,
      dataReceived: !!data.chart?.result?.[0],
      timestamp: new Date().toISOString(),
      sampleData: {
        symbol: data.chart?.result?.[0]?.meta?.symbol,
        price: data.chart?.result?.[0]?.meta?.regularMarketPrice,
        currency: data.chart?.result?.[0]?.meta?.currency,
      }
    });

  } catch (error) {
    console.error('Yahoo Finance test error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to test Yahoo Finance API',
      message: error.message,
      timestamp: new Date().toISOString(),
    });
  }
}
