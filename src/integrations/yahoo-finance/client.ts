import axios from 'axios';

// Yahoo Finance configuration - Use Vercel API route for production
const YAHOO_BASE_URL = import.meta.env.VITE_YAHOO_FINANCE_BASE_URL ||
  (import.meta.env.PROD
    ? '/api/yahoo-finance'  // Use Vercel API route in production
    : 'https://localhost:3443/api/yahoo-finance');
const RATE_LIMIT_MS = parseInt(import.meta.env.VITE_YAHOO_FINANCE_RATE_LIMIT_MS || '2000');

// Rate limiting queue
let lastRequestTime = 0;
const requestQueue: Array<() => Promise<any>> = [];
let isProcessingQueue = false;

const processQueue = async () => {
  if (isProcessingQueue || requestQueue.length === 0) return;
  
  isProcessingQueue = true;
  
  while (requestQueue.length > 0) {
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    
    if (timeSinceLastRequest < RATE_LIMIT_MS) {
      await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_MS - timeSinceLastRequest));
    }
    
    const request = requestQueue.shift();
    if (request) {
      try {
        await request();
      } catch (error) {
        console.error('Yahoo Finance request failed:', error);
      }
      lastRequestTime = Date.now();
    }
  }
  
  isProcessingQueue = false;
};

const rateLimitedRequest = <T>(requestFn: () => Promise<T>): Promise<T> => {
  return new Promise((resolve, reject) => {
    requestQueue.push(async () => {
      try {
        const result = await requestFn();
        resolve(result);
      } catch (error) {
        reject(error);
      }
    });
    processQueue();
  });
};

// Create axios instance
const yahooFinanceClient = axios.create({
  baseURL: YAHOO_BASE_URL,
  timeout: 30000, // 30 second timeout (increased from 15s)
  headers: {
    'Accept': 'application/json',
  },
});

// Enhanced logging for debugging
console.log('Yahoo Finance Client Configuration:', {
  baseUrl: YAHOO_BASE_URL,
  rateLimitMs: RATE_LIMIT_MS,
  environment: import.meta.env.MODE,
  envVars: {
    VITE_YAHOO_FINANCE_BASE_URL: import.meta.env.VITE_YAHOO_FINANCE_BASE_URL,
    VITE_PROXY_URL: import.meta.env.VITE_PROXY_URL,
  }
});

// Request interceptor for logging
yahooFinanceClient.interceptors.request.use(
  (config) => {
    console.log(`📡 Yahoo Finance Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Yahoo Finance Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
yahooFinanceClient.interceptors.response.use(
  (response) => {
    console.log(`✅ Yahoo Finance Response: ${response.config.url} - ${response.status}`);
    return response;
  },
  (error) => {
    const errorDetails = {
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message,
      responseData: error.response?.data
    };

    console.error(`❌ Yahoo Finance API Error: ${error.config?.url}`, errorDetails);

    // Provide more helpful error messages
    if (error.response?.status === 429) {
      console.error('⏰ Rate limit exceeded: Too many requests to Yahoo Finance API');
    } else if (error.response?.status === 404) {
      console.error('🔍 Symbol not found: Check if the symbol is correct');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('🔌 Connection refused: Check internet connection');
    } else if (error.code === 'NETWORK_ERROR') {
      console.error('🌐 Network error: Check CORS settings or use proxy');
    }

    return Promise.reject(error);
  }
);

// Export the rate-limited request function for use in API calls
export { rateLimitedRequest };

export default yahooFinanceClient;
