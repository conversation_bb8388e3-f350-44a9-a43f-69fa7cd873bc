import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, TrendingUp, TrendingDown, RefreshCw } from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
  Bar,
  BarChart,
  ComposedChart,
} from 'recharts';
import { useStockCandles } from '@/hooks/useStockData';
import { useIndianStockHistory } from '@/hooks/useIndianStockData';
import { ChartTimeframe } from '@/integrations/finnhub/types';

interface StockChartProps {
  symbol: string;
  name?: string;
  height?: number;
  showControls?: boolean;
  currentQuote?: any; // Accept quote data as prop to avoid duplicate API calls
}

const timeframeOptions: { value: ChartTimeframe; label: string }[] = [
  { value: '1D', label: '1D' },
  { value: '1W', label: '1W' },
  { value: '1M', label: '1M' },
  { value: '3M', label: '3M' },
  { value: '6M', label: '6M' },
  { value: '1Y', label: '1Y' },
  { value: '5Y', label: '5Y' },
];

const chartTypes = [
  { value: 'line', label: 'Line', icon: TrendingUp },
  { value: 'area', label: 'Area', icon: TrendingUp },
  { value: 'candlestick', label: 'Candle', icon: TrendingDown },
];

const StockChart: React.FC<StockChartProps> = ({
  symbol,
  name,
  height = 400,
  showControls = true,
  currentQuote
}) => {
  const [timeframe, setTimeframe] = useState<ChartTimeframe>('1M');
  const [chartType, setChartType] = useState<'line' | 'area' | 'candlestick'>('area');

  // Determine if this is an Indian stock
  const isIndianStock = symbol.endsWith('.NS') || symbol.endsWith('.BO');

  // Use appropriate data source based on stock type - only fetch when quote is available
  const { data: candleData, isLoading: candleLoading, error: candleError } = useStockCandles(
    symbol,
    timeframe,
    !isIndianStock && !!currentQuote // Only enabled for non-Indian stocks and when quote is available
  );

  const { data: indianHistoryData, isLoading: indianLoading, error: indianError } = useIndianStockHistory(
    symbol,
    timeframe as any, // Convert timeframe format
    isIndianStock && !!currentQuote // Only enabled for Indian stocks and when quote is available
  );

  // currentQuote is now passed as prop to avoid duplicate API calls

  // Combine loading and error states
  const isLoading = isIndianStock ? indianLoading : candleLoading;
  const error = isIndianStock ? indianError : candleError;

  // Don't show loading if we don't have quote data yet
  const shouldShowLoading = isLoading && !!currentQuote;

  // Show error only if we have quote data but chart data failed
  const shouldShowError = error && !!currentQuote;

  // Generate mock chart data as fallback for free tier
  const generateMockChartData = (symbol: string, timeframe: ChartTimeframe, currentPrice?: number) => {
    const basePrice = currentPrice || 150; // Default base price
    const dataPoints = timeframe === '1D' ? 24 : timeframe === '1W' ? 7 : timeframe === '1M' ? 30 : 90;
    const now = Date.now();
    const interval = timeframe === '1D' ? 3600000 : timeframe === '1W' ? 86400000 : 86400000; // 1 hour, 1 day, 1 day

    return Array.from({ length: dataPoints }, (_, index) => {
      const timestamp = now - (dataPoints - index - 1) * interval;
      const randomVariation = (Math.random() - 0.5) * 0.1; // ±5% variation
      const price = basePrice * (1 + randomVariation + (Math.sin(index / 5) * 0.05));
      const volume = Math.floor(Math.random() * 1000000) + 100000;

      return {
        timestamp,
        date: new Date(timestamp).toLocaleDateString(),
        time: new Date(timestamp).toLocaleTimeString(),
        open: price * (1 + (Math.random() - 0.5) * 0.02),
        high: price * (1 + Math.random() * 0.03),
        low: price * (1 - Math.random() * 0.03),
        close: price,
        volume,
      };
    });
  };

  // Transform data for charts with fallback
  const { chartData, isUsingMockData } = React.useMemo(() => {
    console.log('Chart data transformation:', {
      symbol,
      isIndianStock,
      hasIndianData: !!indianHistoryData,
      hasCandleData: !!candleData,
      timeframe,
    });

    if (isIndianStock && indianHistoryData) {
      // Handle Yahoo Finance Indian stock data
      try {
        const result = indianHistoryData.chart?.result?.[0];
        console.log('Processing Indian stock data:', {
          result: !!result,
          indicators: !!result?.indicators,
          timestampCount: result?.timestamp?.length || 0,
          quoteDataLength: result?.indicators?.quote?.[0]?.close?.length || 0
        });

        if (result && result.indicators?.quote?.[0]) {
          const quote = result.indicators.quote[0];
          const timestamps = result.timestamp || [];

          // Check if we have sufficient data
          if (timestamps.length === 0) {
            console.warn('No timestamp data available for', symbol);
          } else if (timestamps.length === 1) {
            console.warn('Only 1 data point available for', symbol, '- this may cause slow loading');
          }

          const transformedData = timestamps.map((timestamp: number, index: number) => ({
            timestamp: timestamp * 1000,
            date: new Date(timestamp * 1000).toLocaleDateString(),
            time: new Date(timestamp * 1000).toLocaleTimeString(),
            open: quote.open?.[index] || 0,
            high: quote.high?.[index] || 0,
            low: quote.low?.[index] || 0,
            close: quote.close?.[index] || 0,
            volume: quote.volume?.[index] || 0,
          })).filter(item => item.close > 0); // Filter out invalid data points

          console.log(`Transformed ${transformedData.length} data points for ${symbol}`);

          if (transformedData.length > 0) {
            return {
              chartData: transformedData,
              isUsingMockData: false
            };
          } else {
            console.warn('No valid data points after transformation for', symbol);
          }
        } else {
          console.warn('No quote data in result for', symbol);
        }
      } catch (error) {
        console.warn('Error processing Indian stock history data:', error);
      }
    } else if (!isIndianStock && candleData && (candleData as any).c && (candleData as any).s === 'ok' && (candleData as any).c.length > 0) {
      // Handle Finnhub US stock data
      const finnhubData = candleData as any;
      const transformedData = finnhubData.c.map((close: number, index: number) => ({
        timestamp: finnhubData.t[index] * 1000,
        date: new Date(finnhubData.t[index] * 1000).toLocaleDateString(),
        time: new Date(finnhubData.t[index] * 1000).toLocaleTimeString(),
        open: finnhubData.o[index],
        high: finnhubData.h[index],
        low: finnhubData.l[index],
        close,
        volume: finnhubData.v[index],
      }));

      return {
        chartData: transformedData,
        isUsingMockData: false
      };
    }

    // Fallback to mock data
    const currentPrice = isIndianStock
      ? (currentQuote as any)?.price
      : (currentQuote as any)?.c;

    return {
      chartData: generateMockChartData(symbol, timeframe, currentPrice),
      isUsingMockData: true
    };
  }, [isIndianStock, indianHistoryData, candleData, symbol, timeframe, currentQuote]);

  const formatPrice = (value: number) => {
    const safeValue = value || 0;
    if (symbol.endsWith('.NS') || symbol.endsWith('.BO')) {
      return `₹${safeValue.toFixed(2)}`;
    }
    return `$${safeValue.toFixed(2)}`;
  };

  const formatVolume = (value: number) => {
    const safeValue = value || 0;
    if (safeValue >= 1000000) {
      return `${(safeValue / 1000000).toFixed(1)}M`;
    } else if (safeValue >= 1000) {
      return `${(safeValue / 1000).toFixed(1)}K`;
    }
    return safeValue.toString();
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-xl backdrop-blur-sm">
          <p className="text-sm font-semibold text-gray-900 mb-1">{data.date}</p>
          <p className="text-xs text-gray-500 mb-3">{data.time}</p>
          {chartType === 'candlestick' ? (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-600">Open:</span>
                <span className="text-sm font-medium text-gray-900">{formatPrice(data.open)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-600">High:</span>
                <span className="text-sm font-medium text-green-600">{formatPrice(data.high)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-600">Low:</span>
                <span className="text-sm font-medium text-red-600">{formatPrice(data.low)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-600">Close:</span>
                <span className="text-sm font-medium text-gray-900">{formatPrice(data.close)}</span>
              </div>
              <div className="flex justify-between items-center pt-1 border-t border-gray-100">
                <span className="text-xs text-gray-600">Volume:</span>
                <span className="text-sm font-medium text-blue-600">{formatVolume(data.volume)}</span>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-600">Price:</span>
                <span className="text-sm font-medium text-gray-900">{formatPrice(data.close)}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-600">Volume:</span>
                <span className="text-sm font-medium text-blue-600">{formatVolume(data.volume)}</span>
              </div>
            </div>
          )}
        </div>
      );
    }
    return null;
  };

  const renderChart = () => {
    if (chartType === 'line') {
      return (
        <LineChart data={chartData}>
          <CartesianGrid
            stroke="#e5e7eb"
            strokeWidth={1}
            horizontal={true}
            vertical={false}
          />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 12, fill: '#6b7280' }}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            domain={['dataMin - 5', 'dataMax + 5']}
            tick={{ fontSize: 12, fill: '#6b7280' }}
            tickLine={false}
            axisLine={false}
            tickFormatter={formatPrice}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{ stroke: '#2563eb', strokeWidth: 1, strokeDasharray: '5 5' }}
          />
          <Line
            type="monotone"
            dataKey="close"
            stroke="#2563eb"
            strokeWidth={2.5}
            dot={false}
            activeDot={{
              r: 6,
              stroke: '#2563eb',
              strokeWidth: 2,
              fill: '#ffffff',
              style: { filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' }
            }}
          />
        </LineChart>
      );
    }

    if (chartType === 'area') {
      return (
        <AreaChart data={chartData}>
          <defs>
            <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#2563eb" stopOpacity={0.4}/>
              <stop offset="95%" stopColor="#2563eb" stopOpacity={0.05}/>
            </linearGradient>
          </defs>
          <CartesianGrid
            stroke="#e5e7eb"
            strokeWidth={1}
            horizontal={true}
            vertical={false}
          />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 12, fill: '#6b7280' }}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            domain={['dataMin - 5', 'dataMax + 5']}
            tick={{ fontSize: 12, fill: '#6b7280' }}
            tickLine={false}
            axisLine={false}
            tickFormatter={formatPrice}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{ stroke: '#2563eb', strokeWidth: 1, strokeDasharray: '5 5' }}
          />
          <Area
            type="monotone"
            dataKey="close"
            stroke="#2563eb"
            strokeWidth={2.5}
            fillOpacity={1}
            fill="url(#colorPrice)"
            dot={false}
            activeDot={{
              r: 6,
              stroke: '#2563eb',
              strokeWidth: 2,
              fill: '#ffffff',
              style: { filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' }
            }}
          />
        </AreaChart>
      );
    }

    // Candlestick chart (simplified using bars)
    return (
      <ComposedChart data={chartData}>
        <CartesianGrid
          stroke="#e5e7eb"
          strokeWidth={1}
          horizontal={true}
          vertical={false}
        />
        <XAxis
          dataKey="date"
          tick={{ fontSize: 12, fill: '#6b7280' }}
          tickLine={false}
          axisLine={false}
        />
        <YAxis
          domain={['dataMin - 5', 'dataMax + 5']}
          tick={{ fontSize: 12, fill: '#6b7280' }}
          tickLine={false}
          axisLine={false}
          tickFormatter={formatPrice}
        />
        <Tooltip
          content={<CustomTooltip />}
          cursor={{ stroke: '#2563eb', strokeWidth: 1, strokeDasharray: '5 5' }}
        />
        <Bar
          dataKey="high"
          fill="#10b981"
          opacity={0.7}
          radius={[1, 1, 0, 0]}
        />
        <Bar
          dataKey="low"
          fill="#ef4444"
          opacity={0.7}
          radius={[0, 0, 1, 1]}
        />
        <Line
          type="monotone"
          dataKey="close"
          stroke="#2563eb"
          strokeWidth={2.5}
          dot={false}
          activeDot={{
            r: 6,
            stroke: '#2563eb',
            strokeWidth: 2,
            fill: '#ffffff',
            style: { filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' }
          }}
        />
      </ComposedChart>
    );
  };

  // Show loading only when we have quote data but chart data is still loading
  if (shouldShowLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center" style={{ height }}>
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading chart data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Only show error if there's an actual error AND no fallback data AND we have quote data
  if (shouldShowError && !chartData.length) {
    console.error('Chart error details:', {
      symbol,
      timeframe,
      isIndianStock,
      error: error.message,
      indianHistoryData: !!indianHistoryData,
      candleData: !!candleData,
    });

    return (
      <Card>
        <CardContent className="flex items-center justify-center" style={{ height }}>
          <div className="text-center space-y-3">
            <p className="text-red-500 mb-2">Chart data temporarily unavailable</p>
            <p className="text-sm text-gray-500">
              {error.message.includes('timeout')
                ? 'API request timed out. Please try again.'
                : `Error: ${error.message}`}
            </p>
            <p className="text-xs text-gray-400">
              Symbol: {symbol} | Timeframe: {timeframe} | Source: {isIndianStock ? 'Yahoo Finance' : 'Finnhub'}
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.reload()}
              className="mt-2"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center space-x-2">
            <span>{name || symbol}</span>
            {currentQuote && (
              <span className={`text-sm px-2 py-1 rounded ${
                (isIndianStock ? (currentQuote as any).change : (currentQuote as any).d) >= 0
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {(isIndianStock ? (currentQuote as any).change : (currentQuote as any).d) >= 0 ? '+' : ''}
                {((isIndianStock ? (currentQuote as any).changePercent : (currentQuote as any).dp) || 0).toFixed(2)}%
              </span>
            )}
            {isUsingMockData && (
              <span className="text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-800">
                Demo Data - {isIndianStock ? 'Yahoo Finance' : 'Finnhub'} API unavailable
              </span>
            )}
          </CardTitle>
          {currentQuote && (
            <div className="text-right">
              <p className="text-2xl font-bold">
                {formatPrice(isIndianStock ? (currentQuote as any).price : (currentQuote as any).c)}
              </p>
              <p className={`text-sm ${
                (isIndianStock ? (currentQuote as any).change : (currentQuote as any).d) >= 0
                  ? 'text-green-600'
                  : 'text-red-600'
              }`}>
                {(isIndianStock ? (currentQuote as any).change : (currentQuote as any).d) >= 0 ? '+' : ''}
                {formatPrice(isIndianStock ? (currentQuote as any).change : (currentQuote as any).d)}
              </p>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {showControls && (
          <div className="flex flex-wrap gap-2 mb-4">
            <div className="flex space-x-1">
              {timeframeOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={timeframe === option.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeframe(option.value)}
                >
                  {option.label}
                </Button>
              ))}
            </div>
            <div className="flex space-x-1 ml-auto">
              {chartTypes.map((type) => (
                <Button
                  key={type.value}
                  variant={chartType === type.value ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setChartType(type.value as any)}
                >
                  {type.label}
                </Button>
              ))}
            </div>
          </div>
        )}
        <ResponsiveContainer width="100%" height={height}>
          {renderChart()}
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default StockChart;
