/**
 * Vercel API route to proxy Yahoo Finance requests
 * Handles CORS and provides a clean interface
 */

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    res.status(405).json({ error: 'Method not allowed' });
    return;
  }

  try {
    // Extract the path and query parameters
    const { path, ...queryParams } = req.query;
    
    if (!path) {
      res.status(400).json({ error: 'Path parameter is required' });
      return;
    }

    // Construct the Yahoo Finance URL
    const yahooPath = Array.isArray(path) ? path.join('/') : path;
    const baseUrl = 'https://query1.finance.yahoo.com/v8/finance';
    const url = new URL(`${baseUrl}/${yahooPath}`);
    
    // Add query parameters
    Object.entries(queryParams).forEach(([key, value]) => {
      if (value) {
        url.searchParams.append(key, value);
      }
    });

    console.log(`Proxying request to: ${url.toString()}`);

    // Make the request to Yahoo Finance
    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json',
        'Accept-Language': 'en-US,en;q=0.9',
      },
    });

    if (!response.ok) {
      throw new Error(`Yahoo Finance API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    // Return the data with CORS headers
    res.status(200).json(data);

  } catch (error) {
    console.error('Yahoo Finance proxy error:', error);
    res.status(500).json({ 
      error: 'Failed to fetch data from Yahoo Finance',
      message: error.message 
    });
  }
}
