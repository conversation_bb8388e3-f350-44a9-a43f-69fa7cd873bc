/**
 * Test component for Yahoo Finance API in production
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

const YahooFinanceProductionTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testYahooFinanceAPI = async () => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      // Test the Vercel API proxy
      const response = await fetch('/api/test-yahoo');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'API test failed');
      }

      setResults(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testDirectYahooCall = async () => {
    setLoading(true);
    setError(null);
    setResults(null);

    try {
      // Test direct call through our proxy
      const response = await fetch('/api/yahoo-finance?path=chart/RELIANCE.NS&interval=1d&period=1d&region=IN');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Direct API call failed');
      }

      setResults({
        success: true,
        message: 'Direct Yahoo Finance call successful',
        dataReceived: !!data.chart?.result?.[0],
        sampleData: {
          symbol: data.chart?.result?.[0]?.meta?.symbol,
          price: data.chart?.result?.[0]?.meta?.regularMarketPrice,
          currency: data.chart?.result?.[0]?.meta?.currency,
        },
        fullResponse: data
      });
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🧪 Yahoo Finance Production Test
            <Badge variant="outline">
              {import.meta.env.PROD ? 'Production' : 'Development'}
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Test Yahoo Finance API integration in production environment
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Test Buttons */}
            <div className="flex gap-4">
              <Button
                onClick={testYahooFinanceAPI}
                disabled={loading}
                variant="default"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                Test API Health
              </Button>

              <Button
                onClick={testDirectYahooCall}
                disabled={loading}
                variant="outline"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <AlertCircle className="h-4 w-4 mr-2" />
                )}
                Test Direct Call
              </Button>
            </div>

            {/* Results */}
            {error && (
              <Card className="border-red-200 bg-red-50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 text-red-600">
                    <XCircle className="h-5 w-5" />
                    <span className="font-medium">Error</span>
                  </div>
                  <p className="text-sm text-red-600 mt-2">{error}</p>
                </CardContent>
              </Card>
            )}

            {results && (
              <Card className={`border-green-200 bg-green-50`}>
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 text-green-600 mb-3">
                    <CheckCircle className="h-5 w-5" />
                    <span className="font-medium">Success</span>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div><strong>Message:</strong> {results.message}</div>
                    <div><strong>Data Received:</strong> {results.dataReceived ? 'Yes' : 'No'}</div>
                    <div><strong>Timestamp:</strong> {results.timestamp}</div>
                    
                    {results.sampleData && (
                      <div className="mt-3">
                        <strong>Sample Data:</strong>
                        <div className="bg-white p-2 rounded border mt-1">
                          <div>Symbol: {results.sampleData.symbol}</div>
                          <div>Price: {results.sampleData.price}</div>
                          <div>Currency: {results.sampleData.currency}</div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Environment Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Environment Info</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Mode:</strong> {import.meta.env.MODE}
                  </div>
                  <div>
                    <strong>Production:</strong> {import.meta.env.PROD ? 'Yes' : 'No'}
                  </div>
                  <div>
                    <strong>Base URL:</strong> {window.location.origin}
                  </div>
                  <div>
                    <strong>Yahoo Finance URL:</strong> {import.meta.env.VITE_YAHOO_FINANCE_BASE_URL || 'Default'}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Instructions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Troubleshooting</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <p><strong>If tests fail:</strong></p>
                  <ul className="list-disc list-inside space-y-1 ml-4">
                    <li>Check if Vercel API routes are deployed</li>
                    <li>Verify CORS headers are set correctly</li>
                    <li>Check browser network tab for detailed errors</li>
                    <li>Ensure Yahoo Finance API is accessible</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default YahooFinanceProductionTest;
